"use client";

import React from 'react';
import { motion } from 'framer-motion';

type TimelineItem = {
  year: string;
  title?: string;
  description?: string;
  isActive?: boolean;
};

const defaultItems: TimelineItem[] = [
  {
    year: "2008",
    title: "Foundation",
    description: "Founded with a vision to transform businesses, we started our journey. Our first client project set the stage for future successes.",
    isActive: true
  },
  {
    year: "2014",
    title: "Expansion",
    description: "Expanded our services and team to meet growing market demands.",
    isActive: false
  },
  {
    year: "2019",
    title: "Innovation",
    description: "Introduced cutting-edge solutions and established key partnerships.",
    isActive: false
  },
  {
    year: "2021",
    title: "Growth",
    description: "Achieved significant milestones and market recognition.",
    isActive: false
  }
];

export function Journey({
  items = defaultItems,
  heading = "Our journey",
  subheading = "Each achievement reflects our commitment to excellence and growth.",
}: {
  items?: TimelineItem[];
  heading?: string;
  subheading?: string;
}) {
  return (
    <section className="relative w-full py-20 bg-gradient-to-br from-teal-900 via-teal-800 to-emerald-900 overflow-hidden">
      <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.header
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          {/* Story Label */}
          <div className="flex items-center gap-2 mb-8">
            <div className="w-2 h-2 bg-white rounded-full"></div>
            <span className="text-white/80 text-sm font-medium tracking-wider uppercase">
              Story
            </span>
          </div>

          <h2 className="text-4xl md:text-6xl font-light text-white mb-6 tracking-tight">
            {heading}
          </h2>
          <p className="text-xl text-white/70 max-w-2xl leading-relaxed">
            {subheading}
          </p>
        </motion.header>

        {/* Timeline */}
        <div className="relative mt-20">
          {/* Horizontal timeline line */}
          <div className="absolute left-0 right-0 top-6 h-px bg-white/20"></div>

          {/* Timeline Items */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-16">
            {items.map((item, idx) => (
              <motion.div
                key={idx}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: idx * 0.15 }}
                viewport={{ once: true }}
                className="relative"
              >
                {/* Timeline dot */}
                <div className="relative z-10 w-3 h-3 bg-white rounded-full mb-8"></div>

                {/* Year */}
                <div className={`text-3xl md:text-4xl font-light mb-4 ${
                  item.isActive ? 'text-white' : 'text-white/40'
                }`}>
                  {item.year}
                </div>

                {/* Description - only show for active item */}
                {item.isActive && item.description && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    viewport={{ once: true }}
                    className="mt-8"
                  >
                    <p className="text-white/80 text-base leading-relaxed max-w-sm">
                      {item.description}
                    </p>
                  </motion.div>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}

export default Journey;
