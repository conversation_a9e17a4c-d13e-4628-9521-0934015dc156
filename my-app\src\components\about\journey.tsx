"use client";

import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

type TimelineItem = {
  year: string;
  title: string;
  description: string;
};

const defaultItems: TimelineItem[] = [
  {
    year: "2016",
    title: "Foundation",
    description: "Founded with a vision to transform businesses, we started our journey. Our first client project set the stage for future successes."
  },
  {
    year: "2017",
    title: "Expansion",
    description: "We expanded our services and built long-term partnerships, refining our approach to innovation and client success."
  },
  {
    year: "2018",
    title: "Innovation",
    description: "Embracing new technologies, we scaled our operations and launched key initiatives that shaped our growth."
  },
  {
    year: "2019",
    title: "Growth",
    description: "We adapted to a changing landscape, leveraging data-driven insights to create meaningful client experiences."
  },
  {
    year: "2020",
    title: "Challenges",
    description: "We navigated through challenges, maintaining our focus on innovation and client success."
  }
];

export function Journey({
  items = defaultItems,
  heading = "Our journey",
  subheading = "Each achievement reflects our commitment to excellence and growth.",
}: {
  items?: TimelineItem[];
  heading?: string;
  subheading?: string;
}) {
  const [activeIndex, setActiveIndex] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return;

      const section = sectionRef.current;
      const rect = section.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // Calculate how much of the section has been scrolled through
      const sectionTop = rect.top;
      const sectionHeight = rect.height;

      // Start activating when section enters viewport
      if (sectionTop <= windowHeight && sectionTop + sectionHeight >= 0) {
        // Calculate progress through the section
        const scrolled = windowHeight - sectionTop;
        const progress = Math.max(0, Math.min(1, scrolled / (sectionHeight * 0.8)));

        // Map progress to timeline items
        const newActiveIndex = Math.min(
          items.length - 1,
          Math.floor(progress * items.length)
        );

        setActiveIndex(newActiveIndex);
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Initial call

    return () => window.removeEventListener('scroll', handleScroll);
  }, [items.length]);

  return (
    <section
      ref={sectionRef}
      className="relative bg-emerald-900"
      style={{ height: '300vh' }} // Tall section for scroll effect like Acelia
    >
      {/* Sticky content container */}
      <div className="sticky top-0 h-screen flex items-center">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-20"
          >
            {/* Story Label */}
            <div className="flex items-center gap-2 mb-8">
              <div className="w-2 h-2 bg-white rounded-full"></div>
              <span className="text-white/80 text-sm font-medium tracking-wider uppercase">
                Story
              </span>
            </div>

            <h2 className="text-4xl md:text-6xl font-light text-white mb-6 tracking-tight">
              {heading}
            </h2>
            <p className="text-xl text-white/70 max-w-2xl leading-relaxed">
              {subheading}
            </p>
          </motion.div>

          {/* Timeline */}
          <div className="relative">
            {/* Horizontal timeline line */}
            <div className="absolute left-0 right-0 top-6 h-px bg-white/20"></div>

            {/* Timeline Years */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-16 mb-16">
              {items.map((item, idx) => (
                <div key={idx} className="relative">
                  {/* Timeline dot */}
                  <motion.div
                    className="relative z-10 w-3 h-3 rounded-full mb-8"
                    animate={{
                      backgroundColor: idx <= activeIndex ? '#ffffff' : 'rgba(255, 255, 255, 0.4)',
                      scale: idx <= activeIndex ? 1.2 : 1
                    }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                  />

                  {/* Year */}
                  <motion.div
                    className="text-2xl md:text-3xl lg:text-4xl font-light whitespace-nowrap"
                    animate={{
                      color: idx <= activeIndex ? '#ffffff' : 'rgba(255, 255, 255, 0.4)'
                    }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                  >
                    {item.year}
                  </motion.div>
                </div>
              ))}
            </div>

            {/* Description Area */}
            <div className="max-w-2xl">
              <motion.div
                key={activeIndex}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, ease: "easeInOut" }}
              >
                <p className="text-white/80 text-lg leading-relaxed">
                  {items[activeIndex]?.description}
                </p>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default Journey;
