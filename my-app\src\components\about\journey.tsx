"use client";

import React, { useState, useEffect } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';

type TimelineItem = {
  year: string;
  title: string;
  description: string;
};

const defaultItems: TimelineItem[] = [
  {
    year: "2008",
    title: "Foundation",
    description: "Founded with a vision to transform businesses, we started our journey. Our first client project set the stage for future successes."
  },
  {
    year: "2014",
    title: "Expansion",
    description: "Expanded our services and team to meet growing market demands. Established our core HVAC expertise and built lasting client relationships."
  },
  {
    year: "2019",
    title: "Innovation",
    description: "Introduced cutting-edge solutions and established key partnerships. Diversified into comprehensive HVAC equipment supply and design services."
  },
  {
    year: "2020",
    title: "Resilience",
    description: "Adapted to global challenges while maintaining service excellence. Strengthened our operations and maintenance capabilities across multiple sectors."
  },
  {
    year: "2021",
    title: "Growth",
    description: "Achieved significant milestones and market recognition. Established partnerships with 12+ brands and 150+ vendors nationwide."
  }
];

export function Journey({
  items = defaultItems,
  heading = "Our journey",
  subheading = "Each achievement reflects our commitment to excellence and growth.",
}: {
  items?: TimelineItem[];
  heading?: string;
  subheading?: string;
}) {
  const [activeIndex, setActiveIndex] = useState(0);
  const sectionRef = React.useRef<HTMLElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return;

      const section = sectionRef.current;
      const rect = section.getBoundingClientRect();
      const sectionHeight = rect.height;
      const sectionTop = rect.top;
      const windowHeight = window.innerHeight;

      // Calculate scroll progress within the section
      const scrollProgress = Math.max(0, Math.min(1, (windowHeight - sectionTop) / (sectionHeight + windowHeight)));

      // Determine which item should be active based on scroll progress
      const newActiveIndex = Math.min(
        items.length - 1,
        Math.floor(scrollProgress * items.length)
      );

      setActiveIndex(newActiveIndex);
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Initial call

    return () => window.removeEventListener('scroll', handleScroll);
  }, [items.length]);

  return (
    <section
      ref={sectionRef}
      className="relative w-full py-20 bg-gradient-to-br from-teal-900 via-teal-800 to-emerald-900 overflow-hidden"
      style={{ minHeight: '200vh' }} // Make section taller for scroll effect
    >
      <div className="sticky top-20 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.header
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          {/* Story Label */}
          <div className="flex items-center gap-2 mb-8">
            <div className="w-2 h-2 bg-white rounded-full"></div>
            <span className="text-white/80 text-sm font-medium tracking-wider uppercase">
              Story
            </span>
          </div>

          <h2 className="text-4xl md:text-6xl font-light text-white mb-6 tracking-tight">
            {heading}
          </h2>
          <p className="text-xl text-white/70 max-w-2xl leading-relaxed">
            {subheading}
          </p>
        </motion.header>

        {/* Timeline */}
        <div className="relative mt-20">
          {/* Horizontal timeline line */}
          <div className="absolute left-0 right-0 top-6 h-px bg-white/20"></div>

          {/* Timeline Items */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-8 md:gap-12">
            {items.map((item, idx) => (
              <motion.div
                key={idx}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: idx * 0.15 }}
                viewport={{ once: true }}
                className="relative"
              >
                {/* Timeline dot */}
                <motion.div
                  className={`relative z-10 w-3 h-3 rounded-full mb-8 transition-all duration-500 ${
                    idx <= activeIndex ? 'bg-white scale-125' : 'bg-white/40'
                  }`}
                  animate={{
                    scale: idx === activeIndex ? 1.5 : idx <= activeIndex ? 1.25 : 1,
                    backgroundColor: idx <= activeIndex ? '#ffffff' : 'rgba(255, 255, 255, 0.4)'
                  }}
                  transition={{ duration: 0.5 }}
                />

                {/* Year */}
                <motion.div
                  className={`text-3xl md:text-4xl font-light mb-4 transition-all duration-500 ${
                    idx <= activeIndex ? 'text-white' : 'text-white/40'
                  }`}
                  animate={{
                    color: idx <= activeIndex ? '#ffffff' : 'rgba(255, 255, 255, 0.4)',
                    scale: idx === activeIndex ? 1.1 : 1
                  }}
                  transition={{ duration: 0.5 }}
                >
                  {item.year}
                </motion.div>
              </motion.div>
            ))}
          </div>

          {/* Active Description */}
          <motion.div
            key={activeIndex}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.6 }}
            className="mt-16 max-w-2xl"
          >
            <h3 className="text-2xl font-semibold text-white mb-4">
              {items[activeIndex]?.title}
            </h3>
            <p className="text-white/80 text-lg leading-relaxed">
              {items[activeIndex]?.description}
            </p>
          </motion.div>
        </div>
      </div>
    </section>
  );
}

export default Journey;
